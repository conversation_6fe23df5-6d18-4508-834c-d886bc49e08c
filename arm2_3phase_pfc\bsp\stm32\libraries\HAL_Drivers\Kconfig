if BSP_USING_USBD
    config BSP_USBD_TYPE_FS
        bool
        # "USB Full Speed (FS) Core"
    config BSP_USBD_TYPE_HS
        bool
        # "USB High Speed (HS) Core"

    config BSP_USBD_SPEED_HS
        bool 
        # "USB High Speed (HS) Mode"
    config BSP_USBD_SPEED_HSINFS
        bool 
        # "USB High Speed (HS) Core in FS mode"

    config BSP_USBD_PHY_EMBEDDED
        bool 
        # "Using Embedded phy interface"
    config BSP_USBD_PHY_UTMI
        bool 
        # "UTMI: USB 2.0 Transceiver Macrocell Interace"
    config BSP_USBD_PHY_ULPI
        bool 
        # "ULPI: UTMI+ Low Pin Interface"
endif

config BSP_USING_CRC
    bool "Enable CRC (CRC-32 0x04C11DB7 Polynomial)"
    select RT_USING_HWCRYPTO
    select RT_HWCRYPTO_USING_CRC
    # "Crypto device frame dose not support above 8-bits granularity"
    # "Reserve progress, running well, about 32-bits granularity, such as stm32f1, stm32f4"
    depends on (SOC_SERIES_STM32L4 || SOC_SERIES_STM32F0 || SOC_SERIES_STM32F7 || SOC_SERIES_STM32H7 || SOC_SERIES_STM32MP1)
    default n 

config BSP_USING_RNG
    bool "Enable RNG (Random Number Generator)"
    select RT_USING_HWCRYPTO
    select RT_HWCRYPTO_USING_RNG
    depends on (SOC_SERIES_STM32L4 || SOC_SERIES_STM32F4 || SOC_SERIES_STM32F7 || \
                SOC_SERIES_STM32H7 || SOC_SERIES_STM32MP1)
    default n

config BSP_USING_HASH
    bool "Enable HASH (Hash House Harriers)"
    select RT_USING_HWCRYPTO
    select RT_HWCRYPTO_USING_HASH
    depends on (SOC_SERIES_STM32MP1)
    default n

config BSP_USING_CRYP
    bool "Enable CRYP (Encrypt And Decrypt Data)"
    select RT_USING_HWCRYPTO
    select RT_HWCRYPTO_USING_CRYP
    depends on (SOC_SERIES_STM32MP1)
    default n

config BSP_USING_UDID
    bool "Enable UDID (Unique Device Identifier)"
    select RT_USING_HWCRYPTO
    default n

