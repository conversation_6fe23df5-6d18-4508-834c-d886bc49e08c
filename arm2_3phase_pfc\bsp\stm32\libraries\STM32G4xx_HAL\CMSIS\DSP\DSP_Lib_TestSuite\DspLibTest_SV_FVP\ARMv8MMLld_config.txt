# Parameters:
# instance.parameter=value       #(type, mode) default = 'def value' : description : [min..max]
#----------------------------------------------------------------------------------------------
cpu0.FPU=0                                            # (bool  , init-time) default = '1'      : Set whether the model has VFP support
cpu0.DSP=1                                            # (bool  , init-time) default = '1'      : Set whether the model has the DSP extension
cpu0.semihosting-enable=0                             # (bool  , init-time) default = '1'      : Enable semihosting SVC traps. Applications that do not use semihosting must set this parameter to false.
cpu0.min_sync_level=0x3                               # (int   , run-time ) default = '0x0'    : force minimum syncLevel (0=off=default,1=syncState,2=postInsnIO,3=postInsnAll) : [0x0..0x3]
cpu0.cpi_mul=0x1                                      # (int   , run-time ) default = '0x1'    : multiplier for calculating CPI (Cycles Per Instruction) : [0x1..0x7FFFFFFF]
cpu0.cpi_div=0x1                                      # (int   , run-time ) default = '0x1'    : divider for calculating CPI (Cycles Per Instruction) : [0x1..0x7FFFFFFF]
cpu0.SECEXT=0                                         # (bool  , init-time) default = '1'      : Whether the ARMv8-M Security Extensions are included
idau.NUM_IDAU_REGION=0x0                              # (int   , init-time) default = '0xA'    : 
fvp_mps2.DISABLE_GATING=1                             # (bool  , init-time) default = '0'      : Disable Memory gating logic
#----------------------------------------------------------------------------------------------
