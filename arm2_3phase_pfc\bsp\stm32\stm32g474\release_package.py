#!/usr/bin/env python3
import xml.etree.ElementTree as ET
import os
import zipfile
import sys

def package_release(tag_name):
    # 解析配置文件
    tree = ET.parse('bsp/stm32/stm32g474/BIN_HEX_CONFIG.xml')
    root = tree.getroot()
    
    output_dir = root.find('.//output_dir').text
    project_name = root.find('.//project_name').text
    
    # 创建发布包
    package_name = f"{project_name}_Firmware_{tag_name}.zip"
    
    with zipfile.ZipFile(package_name, 'w') as zf:
        # 打包hex和bin文件
        for root_dir, dirs, files in os.walk(output_dir):
            for file in files:
                if file.endswith(('.hex', '.bin')):
                    file_path = os.path.join(root_dir, file)
                    zf.write(file_path, file)
    
    print(f"Created: {package_name}")

if __name__ == "__main__":
    tag_name = sys.argv[1] if len(sys.argv) > 1 else "latest"
    package_release(tag_name)