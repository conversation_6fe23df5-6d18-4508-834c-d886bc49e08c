<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Release Notes for STM32G4xx CMSIS</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <link rel="stylesheet" href="_htmresc/mini-st.css" />
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
</head>
<body>
<div class="row">
<div class="col-sm-12 col-lg-4">
<div class="card fluid">
<div class="sectione dark">
<center>
<h1 id="release-notes-for-stm32g4xx-cmsis"><small>Release Notes for</small> STM32G4xx CMSIS</h1>
<p>Copyright © 2019 STMicroelectronics<br />
</p>
<a href="https://www.st.com" class="logo"><img src="_htmresc/st_logo.png" alt="ST logo" /></a>
</center>
</div>
</div>
<h1 id="license">License</h1>
<p>This software component is licensed by ST under Apache 2.0 license, the “License”; You may not use this component except in compliance with the License. You may obtain a copy of the License at:</p>
<p><a href="https://opensource.org/licenses/Apache-2.0">https://opensource.org/licenses/Apache-2.0</a></p>
<h1 id="purpose">Purpose</h1>
<p>This driver provides the CMSIS device for the stm32g4xx products. This covers following series :</p>
<ul>
<li><p>STM32G431/41xx</p></li>
<li><p>STM32G471xx</p></li>
<li><p>STM32G473/83xx</p></li>
<li><p>STM32G474/84xx</p></li>
</ul>
<p>This driver is composed of the descriptions of the registers under “Include” directory.</p>
<p>Various template file are provided to easily build an application. They can be adapted to fit applications requirements.</p>
<ul>
<li>Templates/system_stm32g4xx.c contains the initialization code referred as SystemInit.</li>
<li>Startup files are provided as example for IAR©, KEIL© and SW4STM32©.</li>
<li>Linker files are provided as example for IAR©, KEIL© and SW4STM32©.</li>
</ul>
</div>
<div class="col-sm-12 col-lg-8">
<h1 id="update-history">Update History</h1>
<div class="collapse">
<input type="checkbox" id="collapse-section1_2_1" aria-hidden="true"> <label for="collapse-section1_2_1" aria-hidden="true">V1.2.1 / 11-January-2021</label>
<div>
<h2 id="main-changes">Main Changes</h2>
<h3 id="maintenance-release">Maintenance release</h3>
<ul>
<li>General updates to fix known defects and enhancements implementation</li>
</ul>
<h2 id="contents">Contents</h2>
<table>
<caption>Additional features<br />
</caption>
<thead>
<tr class="header">
<th>Headline</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>- General updates to fix known defects and enhancements implementation.</td>
</tr>
<tr class="even">
<td>- Protect Vector table modification following SRAM or FLASH preprocessor directive by a generic preprocessor directive: USER_VECT_TAB_ADDRESS.</td>
</tr>
</tbody>
</table>
<p>: Fixed bugs list<br />
</p>
<h2 id="known-limitations">Known Limitations</h2>
<h2 id="development-toolchains-and-compilers">Development Toolchains and Compilers</h2>
<ul>
<li>IAR Embedded Workbench for ARM (EWARM) toolchain <strong>V8.50.4</strong> + ST-Link</li>
<li>RealView Microcontroller Development Kit (MDK-ARM) toolchain <strong>V5.31</strong> + ST-Link</li>
<li>CubeIDE toolchain <strong>V1.6.0</strong></li>
</ul>
<h2 id="supported-devices-and-boards">Supported Devices and boards</h2>
<ul>
<li>STM32G431xx, STM32G441xx devices</li>
<li>STM32G471xx devices</li>
<li>STM32G473xx, STM32G483xx devices</li>
<li>STM32G474xx, STM32G484xx devices</li>
<li>STM32G491xx, STM32G4A1xx devices</li>
</ul>
<p>Note: in the section above, main changes are highlighted in <strong>bold</strong> since previous release.</p>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section1_2_0" aria-hidden="true"> <label for="collapse-section1_2_0" aria-hidden="true">V1.2.0 / 26-June-2020</label>
<div>
<h2 id="main-changes-1">Main Changes</h2>
<h3 id="maintenance-release-1">Maintenance release</h3>
<ul>
<li>Add support for STM32G491xx and STM32G4A1 devices</li>
<li>General updates to fix known defects and enhancements implementation</li>
</ul>
<h2 id="contents-1">Contents</h2>
<table>
<caption>Additional features<br />
</caption>
<thead>
<tr class="header">
<th>Headline</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>- Add startup files for STM32G491xx/4A1xx devices</td>
</tr>
<tr class="even">
<td>- Rename <strong>“TIM7_DAC_IRQHandler”</strong> to <strong>“TIM7_IRQHandler”</strong> in MDK-ARM startup file for STM32G431xx/441xx/bk1cb devices</td>
</tr>
<tr class="odd">
<td>- Remove HRTIM_BMTRGR useless constant definitions</td>
</tr>
<tr class="even">
<td>- Remove ADC_CFGR2_LFTRIG useless constant definitions</td>
</tr>
</tbody>
</table>
<p>: Fixed bugs list<br />
</p>
<h2 id="known-limitations-1">Known Limitations</h2>
<h2 id="development-toolchains-and-compilers-1">Development Toolchains and Compilers</h2>
<ul>
<li>IAR Embedded Workbench for ARM (EWARM) toolchain <strong>V8.40.1</strong></li>
<li>RealView Microcontroller Development Kit (MDK-ARM) toolchain V5.27.1</li>
<li>STM32CubeIDE toolchain V1.3.0</li>
</ul>
<h2 id="supported-devices-and-boards-1">Supported Devices and boards</h2>
<ul>
<li>STM32G431xx, STM32G441xx devices</li>
<li>STM32G471xx devices</li>
<li>STM32G473xx, STM32G483xx devices</li>
<li>STM32G474xx, STM32G484xx devices</li>
<li><strong>STM32G491xx, STM32G4A1xx devices</strong></li>
</ul>
<p>Note: in the section above, main changes are highlighted in <strong>bold</strong> since previous release.</p>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section1_1_1" aria-hidden="true"> <label for="collapse-section1_1_1" aria-hidden="true">V1.1.1 / 14-February-2020</label>
<div>
<h2 id="main-changes-2">Main Changes</h2>
<h3 id="maintenance-release-2">Maintenance release</h3>
<p>General updates to fix known defects and enhancements implementation</p>
<h2 id="contents-2">Contents</h2>
<table>
<caption>Additional features<br />
</caption>
<thead>
<tr class="header">
<th style="text-align: left;">Headline</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;">- General updates to fix known defects and enhancements implementation</td>
</tr>
</tbody>
</table>
<table>
<caption>Fixed bugs list<br />
</caption>
<thead>
<tr class="header">
<th>Headline</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>Update STM32G473/483 startup files to support FDCAN2/3 intances</td>
</tr>
<tr class="even">
<td>Remove IS_TIM_SYNCHRO_INSTANCE macro from device header files</td>
</tr>
</tbody>
</table>
<h2 id="known-limitations-2">Known Limitations</h2>
<h2 id="development-toolchains-and-compilers-2">Development Toolchains and Compilers</h2>
<ul>
<li>IAR Embedded Workbench for ARM (EWARM) toolchain V8.32.3</li>
<li>RealView Microcontroller Development Kit (MDK-ARM) toolchain V5.27.1</li>
<li>STM32CubeIDE toolchain V1.3.0</li>
</ul>
<h2 id="supported-devices-and-boards-2">Supported Devices and boards</h2>
<ul>
<li>STM32G431xx, STM32G441xx devices</li>
<li>STM32G471xx devices</li>
<li>STM32G473xx, STM32G483xx devices</li>
<li>STM32G474xx, STM32G484xx devices</li>
</ul>
<p>Note: in the section above, main changes are highlighted in <strong>bold</strong> since previous release.</p>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section1_1_0" aria-hidden="true"> <label for="collapse-section1_1_0" aria-hidden="true">V1.1.0 / 28-June-2019</label>
<div>
<h2 id="main-changes-3">Main Changes</h2>
<h3 id="maintenance-release-3">Maintenance release</h3>
<p>Maintenance release of CMSIS Devices drivers supporting STM32G431xx, STM32G441xx, STM32G471xx, STM32G473xx, <strong>STM32G483xx</strong>, STM32G474xx and STM32G484xx devices</p>
<h2 id="contents-3">Contents</h2>
<table>
<caption>Additional features<br />
</caption>
<thead>
<tr class="header">
<th style="text-align: left;">Headline</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;">[STM32G483xx} New CMSIS driver files to support STM32G483xx</td>
</tr>
<tr class="even">
<td style="text-align: left;">[STM32G471] <strong>Enable ADC3 for STM32G471 product</strong></td>
</tr>
<tr class="odd">
<td style="text-align: left;">[COMP] <strong>Suppress of Deglitcher mode</strong> - not supported on STM32G4 series</td>
</tr>
</tbody>
</table>
<table>
<caption>Fixed bugs list<br />
</caption>
<thead>
<tr class="header">
<th>Headline</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td>[COMP] Suppress of Deglitcher mode not supported on STM32G4 series</td>
</tr>
<tr class="even">
<td>[HRTIM] Wrong definition of HRTIM1_TIMx constants</td>
</tr>
<tr class="odd">
<td>[MDK-ARM] fix issue in startup file : invalid config wizard annotations</td>
</tr>
</tbody>
</table>
<h2 id="known-limitations-3">Known Limitations</h2>
<h2 id="development-toolchains-and-compilers-3">Development Toolchains and Compilers</h2>
<ul>
<li>IAR Embedded Workbench for ARM (EWARM) toolchain V8.20.2</li>
<li>RealView Microcontroller Development Kit (MDK-ARM) toolchain V5.25</li>
<li>System Workbench STM32 (SW4STM32) toolchain V2.7.2</li>
</ul>
<h2 id="supported-devices-and-boards-3">Supported Devices and boards</h2>
<ul>
<li>STM32G431xx, STM32G441xx devices</li>
<li>STM32G471xx devices</li>
<li>STM32G473xx, <strong>STM32G483xx</strong> devices</li>
<li>STM32G474xx, STM32G484xx devices</li>
</ul>
<p>Note: in the section above, main changes are highlighted in <strong>bold</strong> since previous release.</p>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section1_0_0" aria-hidden="true"> <label for="collapse-section1_0_0" aria-hidden="true">V1.0.0 / 12-April-2019</label>
<div>
<h2 id="main-changes-4">Main Changes</h2>
<h3 id="first-release">First release</h3>
<p>First official release for STM32G4xx devices</p>
<h2 id="contents-4">Contents</h2>
<p>CMSIS devices files for STM32G431xx, STM32G441xx, STM32G471xx, STM32G473xx, STM32G474xx and STM32G484xx.</p>
<h2 id="known-limitations-4">Known Limitations</h2>
<table>
<thead>
<tr class="header">
<th style="text-align: left;">Headline</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;">Support of STM32G483xx device in CMSIS</td>
</tr>
</tbody>
</table>
<h2 id="development-toolchains-and-compilers-4">Development Toolchains and Compilers</h2>
<ul>
<li>IAR Embedded Workbench for ARM (EWARM) toolchain V8.20.2</li>
<li>RealView Microcontroller Development Kit (MDK-ARM) toolchain V5.25</li>
<li>System Workbench STM32 (SW4STM32) toolchain V2.7.2</li>
</ul>
<h2 id="supported-devices-and-boards-4">Supported Devices and boards</h2>
<ul>
<li>STM32G431xx, STM32G441xx devices</li>
<li>STM32G471xx devices</li>
<li>STM32G473xx devices</li>
<li>STM32G474xx, STM32G484xx devices</li>
</ul>
</div>
</div>
</div>
</div>
<footer class="sticky">
<p>For complete documentation on STM32G4xx, visit: [<a href="http://www.st.com/stm32g4">www.st.com/stm32g4</a>]</p>
</footer>
</body>
</html>
