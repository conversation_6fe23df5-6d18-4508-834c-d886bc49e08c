{"configurations": [{"name": "29K_Merak", "includePath": ["e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\applications", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\at\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\at\\at_socket", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\at_device-latest\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\at_device-latest\\class\\esp8266", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\libcpu\\arm\\common", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\libcpu\\arm\\cortex-m4", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\hwcrypto", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\spi", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\spi\\sfud\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\board", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\board\\CubeMX_Config\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\HAL_Drivers", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\HAL_Drivers\\config", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\fal-v0.5.0\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\dfs\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\dfs\\filesystems\\devfs", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\finsh", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\libc\\compilers\\common", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\libc\\compilers\\common\\nogcc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\STM32G4xx_HAL_Driver\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Device\\ST\\STM32G4xx\\Include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\littlefs-latest", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\netutils-v1.3.1\\ntp", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\netdev\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\include\\socket", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\impl", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\include\\socket\\sys_socket", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\examples\\utest\\testcases\\kernel", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\utilities\\ulog", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb01_mcsdk\\MCSDK_v5.Y.2-Full\\MotorControl\\MCSDK\\MCLib\\Any\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb01_mcsdk\\MCSDK_v5.Y.2-Full\\MotorControl\\MCSDK\\MCLib\\G4xx\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb01_mcsdk\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\FlashDB-v1.1.0\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\rtu", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\ascii", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\tcp", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\port", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\BsmProtocol", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\CRC", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pfc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\DSP\\Include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\misc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\mtd", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\rtc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\serial", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\spi\\sfud\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\watchdog", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\board\\CubeMX_Config\\Src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Device\\ST\\STM32G4xx\\Source\\Templates\\arm", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\fal-v0.5.0\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\fal-v0.5.0\\samples\\porting", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\dfs\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\STM32G4xx_HAL_Driver\\Src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Device\\ST\\STM32G4xx\\Source\\Templates", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\utilities\\ulog\\backend", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\FlashDB-v1.1.0\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\FlashDB-v1.1.0\\samples"], "defines": ["MERAK_29K", "USE_HAL_DRIVER", "LFS_CONFIG=lfs_config.h", "__RTTHREAD__", "STM32G474xx", "__CLK_TCK=RT_TICK_PER_SECOND", "ARM_MATH_CM4", "__alignof__(x)=", "__asm(x)=", "__asm__(x)=", "__forceinline=", "__restrict=", "__volatile__=", "__inline=", "__inline__=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__unaligned=", "__promise(x)=", "__irq=", "__swi=", "__weak=", "__register=", "__pure=", "__value_in_regs=", "__breakpoint(x)=", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__enable_fiq()=", "__enable_irq()=", "__force_stores()=", "__memory_changed()=", "__schedule_barrier()=", "__semihost(x,y)=0", "__vfp_status(x,y)=0", "__builtin_arm_nop()=", "__builtin_arm_wfi()=", "__builtin_arm_wfe()=", "__builtin_arm_sev()=", "__builtin_arm_sevl()=", "__builtin_arm_yield()=", "__builtin_arm_isb(x)=", "__builtin_arm_dsb(x)=", "__builtin_arm_dmb(x)=", "__builtin_bswap32(x)=0U", "__builtin_bswap16(x)=0U", "__builtin_arm_rbit(x)=0U", "__builtin_clz(x)=0U", "__builtin_arm_ldrex(x)=0U", "__builtin_arm_strex(x,y)=0U", "__builtin_arm_clrex()=", "__builtin_arm_ssat(x,y)=0U", "__builtin_arm_usat(x,y)=0U", "__builtin_arm_ldaex(x)=0U", "__builtin_arm_stlex(x,y)=0U", "__GNUC__=4", "__GNUC_MINOR__=2", "__GNUC_PATCHLEVEL__=1"], "intelliSenseMode": "${default}"}, {"name": "36K_Common", "includePath": ["e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\applications", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\at\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\at\\at_socket", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\at_device-latest\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\at_device-latest\\class\\esp8266", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\libcpu\\arm\\common", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\libcpu\\arm\\cortex-m4", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\hwcrypto", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\spi", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\spi\\sfud\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\board", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\board\\CubeMX_Config\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\HAL_Drivers", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\HAL_Drivers\\config", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\fal-v0.5.0\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\dfs\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\dfs\\filesystems\\devfs", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\finsh", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\libc\\compilers\\common", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\libc\\compilers\\common\\nogcc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\STM32G4xx_HAL_Driver\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Device\\ST\\STM32G4xx\\Include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\littlefs-latest", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\netutils-v1.3.1\\ntp", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\netdev\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\include\\socket", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\impl", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\net\\sal_socket\\include\\socket\\sys_socket", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\examples\\utest\\testcases\\kernel", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\utilities\\ulog", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb01_mcsdk\\MCSDK_v5.Y.2-Full\\MotorControl\\MCSDK\\MCLib\\Any\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb01_mcsdk\\MCSDK_v5.Y.2-Full\\MotorControl\\MCSDK\\MCLib\\G4xx\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb01_mcsdk\\Inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\FlashDB-v1.1.0\\inc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\rtu", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\ascii", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\modbus\\tcp", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\freemodbus-latest\\port", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\BsmProtocol", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\CRC", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pfc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\DSP\\Include", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\pvpb", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\misc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\mtd", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\rtc", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\serial", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\spi\\sfud\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\drivers\\watchdog", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\board\\CubeMX_Config\\Src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Device\\ST\\STM32G4xx\\Source\\Templates\\arm", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\fal-v0.5.0\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\fal-v0.5.0\\samples\\porting", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\dfs\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\STM32G4xx_HAL_Driver\\Src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\libraries\\STM32G4xx_HAL\\CMSIS\\Device\\ST\\STM32G4xx\\Source\\Templates", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\components\\utilities\\ulog\\backend", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\FlashDB-v1.1.0\\src", "e:\\repos\\PVPB02\\arm2_3phase_pfc\\bsp\\stm32\\stm32g474\\packages\\FlashDB-v1.1.0\\samples"], "defines": ["USE_HAL_DRIVER", "LFS_CONFIG=lfs_config.h", "__RTTHREAD__", "STM32G474xx", "__CLK_TCK=RT_TICK_PER_SECOND", "ARM_MATH_CM4", "__alignof__(x)=", "__asm(x)=", "__asm__(x)=", "__forceinline=", "__restrict=", "__volatile__=", "__inline=", "__inline__=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__unaligned=", "__promise(x)=", "__irq=", "__swi=", "__weak=", "__register=", "__pure=", "__value_in_regs=", "__breakpoint(x)=", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__enable_fiq()=", "__enable_irq()=", "__force_stores()=", "__memory_changed()=", "__schedule_barrier()=", "__semihost(x,y)=0", "__vfp_status(x,y)=0", "__builtin_arm_nop()=", "__builtin_arm_wfi()=", "__builtin_arm_wfe()=", "__builtin_arm_sev()=", "__builtin_arm_sevl()=", "__builtin_arm_yield()=", "__builtin_arm_isb(x)=", "__builtin_arm_dsb(x)=", "__builtin_arm_dmb(x)=", "__builtin_bswap32(x)=0U", "__builtin_bswap16(x)=0U", "__builtin_arm_rbit(x)=0U", "__builtin_clz(x)=0U", "__builtin_arm_ldrex(x)=0U", "__builtin_arm_strex(x,y)=0U", "__builtin_arm_clrex()=", "__builtin_arm_ssat(x,y)=0U", "__builtin_arm_usat(x,y)=0U", "__builtin_arm_ldaex(x)=0U", "__builtin_arm_stlex(x,y)=0U", "__GNUC__=4", "__GNUC_MINOR__=2", "__GNUC_PATCHLEVEL__=1"], "intelliSenseMode": "${default}"}], "version": 4}