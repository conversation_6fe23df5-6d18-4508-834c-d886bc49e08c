/* ----------------------------------------------------------------------
 * Project:      CMSIS DSP Library
 * Title:        SupportFunctions.c
 * Description:  Combination of all support function source files.
 *
 * $Date:        18. March 2019
 * $Revision:    V1.0.0
 *
 * Target Processor: Cortex-M cores
 * -------------------------------------------------------------------- */
/*
 * Copyright (C) 2019 ARM Limited or its affiliates. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the License); you may
 * not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an AS IS BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "arm_copy_f32.c"
#include "arm_copy_q15.c"
#include "arm_copy_q31.c"
#include "arm_copy_q7.c"
#include "arm_fill_f32.c"
#include "arm_fill_q15.c"
#include "arm_fill_q31.c"
#include "arm_fill_q7.c"
#include "arm_float_to_q15.c"
#include "arm_float_to_q31.c"
#include "arm_float_to_q7.c"
#include "arm_q15_to_float.c"
#include "arm_q15_to_q31.c"
#include "arm_q15_to_q7.c"
#include "arm_q31_to_float.c"
#include "arm_q31_to_q15.c"
#include "arm_q31_to_q7.c"
#include "arm_q7_to_float.c"
#include "arm_q7_to_q15.c"
#include "arm_q7_to_q31.c"
